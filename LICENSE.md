# Luminari MUD License

*See also docs/legal/

**Custom Luminari MUD code is released into the public domain under The Unlicense.**

## Open Game License (OGL) Content

This software includes game mechanics and systems derived from the d20 System and other materials published under the Open Game License v1.0a. All such content is used in accordance with the terms of the OGL.

## Intellectual Property Notice

This software contains references to various intellectual properties including, but not limited to:
- Dungeons & Dragons® (property of Wizards of the Coast LLC)
- Pathfinder® (property of Paizo Inc.)
- Dragonlance® (property of Wizards of the Coast LLC)
- Forgotten Realms® (property of Wizards of the Coast LLC)

**IMPORTANT DISCLAIMER:** All references to the above properties and any other trademarked content are made solely for purposes of compatibility, reference, and fan appreciation under the principles of fair use. This software and its developers claim no ownership of these properties and have no official affiliation, endorsement, sponsorship, or other connection with Wizards of the Coast LLC, Paizo Inc., or any other rights holders.

The use of these references is believed to constitute fair use under copyright law as:
1. The use is transformative and for non-commercial, educational purposes
2. The references are used only as necessary for compatibility and gameplay
3. The use does not negatively impact the market for the original works
4. This is a non-profit, fan-created work

All trademarks, registered trademarks, proper names, characters, places, and other elements referenced herein are the property of their respective owners and are used without permission. No challenge to their status or ownership is intended or should be inferred.

Any custom code made on LuminariMUD is free and unencumbered software released into the public domain:

Anyone is free to copy, modify, publish, use, compile, sell, or distribute this software, either in source code form or as a compiled binary, for any purpose, commercial or non-commercial, and by any means.

In jurisdictions that recognize copyright laws, the author or authors of this software dedicate any and all copyright interest in the software to the public domain. We make this dedication for the benefit of the public at large and to the detriment of our heirs and successors. We intend this dedication to be an overt act of relinquishment in perpetuity of all present and future rights to this software under copyright law.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

For more information, please refer to <https://unlicense.org>

**Important Note:** This public domain dedication applies ONLY to custom Luminari MUD code. All inherited code from tbaMUD, CircleMUD, and DikuMUD remains subject to their respective licenses.  Those licenses may be able to change, so you should check them periodically at their sources and not rely on our docs/legal folder.