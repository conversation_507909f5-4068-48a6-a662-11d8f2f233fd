/*/ \ / \ / \ / \ / \ / \ / \ / \ / \ / \ / \ / \ / \ / \ / \ / \
\                                                             
/  Luminari Mail System                                                           
/  Created By: Gicker                                                           
\                                                             
/                                                             
\         todo:                                                    
/                                                                                                                                                                                       
\ / \ / \ / \ / \ / \ / \ / \ / \ / \ / \ / \ / \ / \ / \ / \ /*/

/* adjusted to return number of NEW mail and added 'silent' mode -zusuk */
int new_mail_alert(struct char_data *ch, bool silent);

ACMD_DECL(do_new_mail);

/*eof*/
