Checks: >
  -*,
  clang-analyzer-*,
  bugprone-*,
  performance-*,
  portability-*,
  -bugprone-macro-parentheses,
  -bugprone-reserved-identifier,
  -bugprone-easily-swappable-parameters,
  -clang-analyzer-security.insecureAPI.DeprecatedOrUnsafeBufferHandling,
  -clang-analyzer-security.insecureAPI.strcpy
# Start with critical errors only, uncomment for stricter checking:
# WarningsAsErrors: '*'
# WarningsAsErrors: >
#   clang-analyzer-core.*,
#   clang-analyzer-unix.*,
#   bugprone-use-after-move,
#   bugprone-infinite-loop,
#   performance-move-const-arg
HeaderFilterRegex: 'src/.*'